// Debug Encryption Issues - Console Test Script
// Run this in the browser console to debug encryption session initialization

class EncryptionDebugger {
  constructor() {
    this.results = [];
  }

  log(message, type = 'info') {
    const timestamp = new Date().toISOString();
    const logMessage = `[${timestamp}] [${type.toUpperCase()}] ${message}`;
    console.log(logMessage);
    this.results.push({ timestamp, type, message });
  }

  async testCryptoSupport() {
    this.log('Testing browser crypto support...', 'test');
    
    try {
      // Test if CryptoUtils is available
      if (typeof window.CryptoUtils === 'undefined') {
        this.log('CryptoUtils not found in window object', 'error');
        return false;
      }

      const support = await window.CryptoUtils.checkCryptoSupport();
      this.log(`Crypto support: ${JSON.stringify(support)}`, 'info');
      
      if (!support.supportsX25519 && !support.supportsECDH) {
        this.log('No supported key exchange algorithms', 'error');
        return false;
      }

      this.log('Crypto support check passed', 'success');
      return true;
    } catch (error) {
      this.log(`Crypto support test failed: ${error.message}`, 'error');
      return false;
    }
  }

  async testKeyGeneration() {
    this.log('Testing key generation...', 'test');
    
    try {
      // Test key pair generation
      const keyPair = await window.CryptoUtils.generateKeyPair();
      this.log('Key pair generated successfully', 'success');
      
      // Test signing key pair generation
      const signingKeyPair = await window.CryptoUtils.generateSigningKeyPair();
      this.log('Signing key pair generated successfully', 'success');
      
      // Test key export
      const publicKey = await window.CryptoUtils.exportPublicKey(keyPair.publicKey);
      this.log(`Public key exported, length: ${publicKey.length}`, 'info');
      
      return { keyPair, signingKeyPair, publicKey };
    } catch (error) {
      this.log(`Key generation test failed: ${error.message}`, 'error');
      return null;
    }
  }

  async testKeyImport(publicKeyData, algorithm) {
    this.log(`Testing key import with algorithm: ${algorithm}...`, 'test');
    
    try {
      const importedKey = await window.CryptoUtils.importPublicKey(publicKeyData, algorithm);
      this.log('Key import successful', 'success');
      return importedKey;
    } catch (error) {
      this.log(`Key import failed: ${error.message}`, 'error');
      return null;
    }
  }

  async testSignalProtocol() {
    this.log('Testing SignalProtocol initialization...', 'test');
    
    try {
      // Check if SignalProtocol is available
      if (typeof window.SignalProtocol === 'undefined') {
        this.log('SignalProtocol not found in window object', 'error');
        return false;
      }

      const signalProtocol = new window.SignalProtocol();
      await signalProtocol.initializeKeys();
      this.log('SignalProtocol initialized successfully', 'success');
      
      const keyBundle = await signalProtocol.getKeyBundle();
      this.log('Key bundle generated successfully', 'success');
      this.log(`Key bundle structure: ${JSON.stringify({
        hasIdentityKey: !!keyBundle.identityKey,
        hasSignedPreKey: !!keyBundle.signedPreKey,
        hasOneTimePreKey: !!keyBundle.oneTimePreKey,
      })}`, 'info');
      
      return { signalProtocol, keyBundle };
    } catch (error) {
      this.log(`SignalProtocol test failed: ${error.message}`, 'error');
      return null;
    }
  }

  async testSessionInitialization() {
    this.log('Testing session initialization between two users...', 'test');
    
    try {
      // Create two SignalProtocol instances (Alice and Bob)
      const alice = new window.SignalProtocol();
      const bob = new window.SignalProtocol();
      
      await alice.initializeKeys();
      await bob.initializeKeys();
      this.log('Both users initialized', 'info');
      
      // Get Bob's key bundle for Alice to use
      const bobKeyBundle = await bob.getKeyBundle();
      this.log('Got Bob\'s key bundle', 'info');
      
      // Alice initializes session with Bob
      const conversationId = 'test-conversation-' + Date.now();
      const sessionState = await alice.initializeSession(conversationId, bobKeyBundle);
      this.log('Session initialized successfully', 'success');
      this.log(`Session state: ${JSON.stringify({
        hasRootKey: !!sessionState.rootKey,
        messageNumberSend: sessionState.messageNumberSend,
        messageNumberReceive: sessionState.messageNumberReceive,
      })}`, 'info');
      
      return { alice, bob, sessionState, conversationId };
    } catch (error) {
      this.log(`Session initialization test failed: ${error.message}`, 'error');
      console.error('Full error:', error);
      return null;
    }
  }

  async testMessageEncryption(alice, conversationId) {
    this.log('Testing message encryption...', 'test');
    
    try {
      const testMessage = 'Hello, this is a test message!';
      const encryptedMessage = await alice.encryptMessage(conversationId, testMessage);
      this.log('Message encrypted successfully', 'success');
      this.log(`Encrypted message structure: ${JSON.stringify({
        hasEncryptedContent: !!encryptedMessage.encryptedContent,
        hasIv: !!encryptedMessage.iv,
        messageNumber: encryptedMessage.messageNumber,
      })}`, 'info');
      
      return encryptedMessage;
    } catch (error) {
      this.log(`Message encryption test failed: ${error.message}`, 'error');
      return null;
    }
  }

  async runFullDebugTest() {
    this.log('Starting comprehensive encryption debug test...', 'start');
    
    try {
      // Test 1: Crypto support
      const cryptoSupported = await this.testCryptoSupport();
      if (!cryptoSupported) {
        this.log('Crypto support test failed, stopping', 'error');
        return;
      }

      // Test 2: Key generation
      const keyGenResult = await this.testKeyGeneration();
      if (!keyGenResult) {
        this.log('Key generation test failed, stopping', 'error');
        return;
      }

      // Test 3: Key import (using generated key)
      const algorithm = window.CryptoUtils.getKeyAlgorithm(keyGenResult.keyPair.privateKey);
      const importResult = await this.testKeyImport(keyGenResult.publicKey, algorithm);
      if (!importResult) {
        this.log('Key import test failed, stopping', 'error');
        return;
      }

      // Test 4: SignalProtocol
      const signalResult = await this.testSignalProtocol();
      if (!signalResult) {
        this.log('SignalProtocol test failed, stopping', 'error');
        return;
      }

      // Test 5: Session initialization
      const sessionResult = await this.testSessionInitialization();
      if (!sessionResult) {
        this.log('Session initialization test failed, stopping', 'error');
        return;
      }

      // Test 6: Message encryption
      const encryptionResult = await this.testMessageEncryption(
        sessionResult.alice, 
        sessionResult.conversationId
      );
      if (!encryptionResult) {
        this.log('Message encryption test failed', 'error');
        return;
      }

      this.log('All encryption debug tests passed! ✅', 'success');
      this.printSummary();

    } catch (error) {
      this.log(`Debug test failed: ${error.message}`, 'error');
      console.error('Full error:', error);
      this.printSummary();
    }
  }

  printSummary() {
    console.log('\n=== Encryption Debug Summary ===');
    this.results.forEach(result => {
      const icon = result.type === 'error' ? '❌' : 
                   result.type === 'success' ? '✅' : 
                   result.type === 'test' ? '🧪' : 'ℹ️';
      console.log(`${icon} ${result.message}`);
    });
    console.log('=== End Debug Summary ===\n');
  }
}

// Instructions
console.log(`
🔍 Encryption Debug Tool
========================

To run the debug test:
1. Make sure you're logged into the chat app
2. Run: const debugger = new EncryptionDebugger(); debugger.runFullDebugTest();

To run individual tests:
- Crypto support: debugger.testCryptoSupport();
- Key generation: debugger.testKeyGeneration();
- SignalProtocol: debugger.testSignalProtocol();
- Session init: debugger.testSessionInitialization();

This will help identify exactly where the encryption is failing.
`);

// Make available globally
window.EncryptionDebugger = EncryptionDebugger;
