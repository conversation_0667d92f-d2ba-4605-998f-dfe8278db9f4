// frontend/src/components/Chat/__tests__/MessageList-decryption.test.tsx
import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import { vi } from 'vitest';
import MessageList from '../MessageList';
import messageReducer from '../../../store/slices/messageSlice';
import { api } from '../../../services/api';

// Mock the contexts
const mockEncryptionContext = {
  isInitialized: true,
  decryptMessage: vi.fn(),
  initializeEncryption: vi.fn(),
  encryptMessage: vi.fn(),
  initializeConversationSession: vi.fn(),
  getSessionState: vi.fn(),
  generateAndUploadPreKeys: vi.fn(),
  rotateKeys: vi.fn(),
  clearEncryption: vi.fn(),
};

const mockSocketContext = {
  retryFailedMessage: vi.fn(),
  markMessageAsRead: vi.fn(),
  sendMessage: vi.fn(),
  createConversation: vi.fn(),
  joinConversation: vi.fn(),
  leaveConversation: vi.fn(),
  startTyping: vi.fn(),
  stopTyping: vi.fn(),
  isConnected: true,
};

vi.mock('../../../contexts/EncryptionContext', () => ({
  useEncryption: () => mockEncryptionContext,
}));

vi.mock('../../../contexts/SocketContext', () => ({
  useSocket: () => mockSocketContext,
}));

// Mock the message API
vi.mock('../../../services/messageApi', () => ({
  useGetMessagesQuery: vi.fn(() => ({
    data: { results: [] },
    isLoading: false,
    error: null,
  })),
}));

describe('MessageList Decryption', () => {
  let store: any;

  beforeEach(() => {
    store = configureStore({
      reducer: {
        messages: messageReducer,
        api: api.reducer,
      },
      middleware: (getDefaultMiddleware) =>
        getDefaultMiddleware().concat(api.middleware),
    });

    vi.clearAllMocks();
  });

  it('should decrypt encrypted messages and display decrypted content', async () => {
    // Mock decryption to return decrypted content
    mockEncryptionContext.decryptMessage.mockResolvedValue('Hello, this is a decrypted message!');

    const mockMessages = [
      {
        id: 'msg-1',
        conversationId: 'conv-1',
        sender: {
          id: 'user-1',
          username: 'alice',
          first_name: 'Alice',
          last_name: 'Smith',
          profile_picture: undefined,
        },
        content: '', // No content initially
        encryptedContent: 'encrypted-data-here',
        iv: 'nonce-data-here',
        messageNumber: 0,
        senderRatchetKey: 'ratchet-key-here',
        previousChainLength: 0,
        messageType: 'TEXT',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
    ];

    // Add messages to Redux store
    store.dispatch({
      type: 'messages/setMessages',
      payload: {
        conversationId: 'conv-1',
        messages: mockMessages,
      },
    });

    render(
      <Provider store={store}>
        <MessageList
          conversationId="conv-1"
          currentUserId="user-2"
        />
      </Provider>
    );

    // Initially should show placeholder
    expect(screen.getByText('[Encrypted message - decryption in progress...]')).toBeInTheDocument();

    // Wait for decryption to complete
    await waitFor(() => {
      expect(screen.getByText('Hello, this is a decrypted message!')).toBeInTheDocument();
    });

    // Verify decryption was called with correct parameters
    expect(mockEncryptionContext.decryptMessage).toHaveBeenCalledWith('conv-1', {
      encryptedContent: 'encrypted-data-here',
      iv: 'nonce-data-here',
      messageNumber: 0,
      ratchetKey: 'ratchet-key-here',
      previousChainLength: 0,
    });
  });

  it('should handle decryption errors gracefully', async () => {
    // Mock decryption to fail
    mockEncryptionContext.decryptMessage.mockRejectedValue(new Error('Decryption failed'));

    const mockMessages = [
      {
        id: 'msg-1',
        conversationId: 'conv-1',
        sender: {
          id: 'user-1',
          username: 'alice',
          first_name: 'Alice',
          last_name: 'Smith',
          profile_picture: undefined,
        },
        content: '',
        encryptedContent: 'encrypted-data-here',
        iv: 'nonce-data-here',
        messageNumber: 0,
        senderRatchetKey: 'ratchet-key-here',
        previousChainLength: 0,
        messageType: 'TEXT',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
    ];

    store.dispatch({
      type: 'messages/setMessages',
      payload: {
        conversationId: 'conv-1',
        messages: mockMessages,
      },
    });

    render(
      <Provider store={store}>
        <MessageList
          conversationId="conv-1"
          currentUserId="user-2"
        />
      </Provider>
    );

    // Wait for decryption error to be handled
    await waitFor(() => {
      expect(screen.getByText('[Failed to decrypt message]')).toBeInTheDocument();
    });
  });

  it('should not attempt to decrypt messages that already have content', () => {
    const mockMessages = [
      {
        id: 'msg-1',
        conversationId: 'conv-1',
        sender: {
          id: 'user-1',
          username: 'alice',
          first_name: 'Alice',
          last_name: 'Smith',
          profile_picture: undefined,
        },
        content: 'This message is already decrypted',
        encryptedContent: 'encrypted-data-here',
        iv: 'nonce-data-here',
        messageNumber: 0,
        senderRatchetKey: 'ratchet-key-here',
        previousChainLength: 0,
        messageType: 'TEXT',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
    ];

    store.dispatch({
      type: 'messages/setMessages',
      payload: {
        conversationId: 'conv-1',
        messages: mockMessages,
      },
    });

    render(
      <Provider store={store}>
        <MessageList
          conversationId="conv-1"
          currentUserId="user-2"
        />
      </Provider>
    );

    // Should display the existing content
    expect(screen.getByText('This message is already decrypted')).toBeInTheDocument();

    // Should not attempt decryption
    expect(mockEncryptionContext.decryptMessage).not.toHaveBeenCalled();
  });
});
