// frontend/src/utils/signalProtocol.ts
import { CryptoUtils, type SodiumKeyPair } from './crypto';

export interface KeyBundle {
  identityKey: string;
  signedPreKey: {
    id: number;
    publicKey: string;
    signature: string;
  };
  oneTimePreKey?: {
    id: number;
    publicKey: string;
  };
}

export interface SessionState {
  rootKey: string;
  chainKeySend?: string;
  chainKeyReceive?: string;
  messageNumberSend: number;
  messageNumberReceive: number;
  previousChainLength: number;
  ratchetKeyPair?: {
    private: string;
    public: string;
  };
  remoteRatchetKey?: string;
}

export interface EncryptedMessage {
  encryptedContent: string;
  iv: string;
  messageNumber: number;
  ratchetKey: string;
  previousChainLength: number;
}

export class SignalProtocol {
  private identityKeyPair: SodiumKeyPair | null = null;
  private signedPreKeyPair: SodiumKeyPair | null = null;
  private signingKeyPair: SodiumKeyPair | null = null;
  private oneTimePreKeys: Map<number, SodiumKeyPair> = new Map();
  private sessions: Map<string, SessionState> = new Map();

  /**
   * Initialize user's key pairs
   */
  async initializeKeys(): Promise<void> {
    console.log('🔐 Initializing cryptographic keys...');

    // Generate identity key pair (X25519)
    this.identityKeyPair = await CryptoUtils.generateKeyPair();
    console.log(`🔐 Identity key algorithm: ${CryptoUtils.getKeyAlgorithm(this.identityKeyPair.privateKey)}`);

    // Generate signing key pair (Ed25519) for signing pre-keys
    this.signingKeyPair = await CryptoUtils.generateSigningKeyPair();
    console.log(`🔐 Signing key algorithm: ${CryptoUtils.getKeyAlgorithm(this.signingKeyPair.privateKey)}`);

    // Generate signed pre-key pair (X25519)
    this.signedPreKeyPair = await CryptoUtils.generateKeyPair();

    // Generate one-time pre-keys (X25519)
    for (let i = 0; i < 100; i++) {
      const keyPair = await CryptoUtils.generateKeyPair();
      this.oneTimePreKeys.set(i, keyPair);
    }

    console.log('🔐 ✅ All cryptographic keys initialized successfully');
  }

  /**
   * Get the algorithm being used for key exchange (always X25519 with libsodium)
   */
  private getKeyExchangeAlgorithm(): string {
    return 'X25519';
  }

  /**
   * Get the algorithm being used for signing (always Ed25519 with libsodium)
   */
  private getSigningAlgorithm(): string {
    return 'Ed25519';
  }

  /**
   * Get user's public key bundle for sharing
   */
  async getKeyBundle(): Promise<KeyBundle> {
    if (!this.identityKeyPair || !this.signedPreKeyPair || !this.signingKeyPair) {
      throw new Error('Keys not initialized');
    }

    const identityPublicKey = await CryptoUtils.exportPublicKey(this.identityKeyPair.publicKey);
    const signedPreKeyPublic = await CryptoUtils.exportPublicKey(this.signedPreKeyPair.publicKey);

    // Sign the pre-key with identity signing key (sign the raw bytes, not the base64 string)
    const signature = await CryptoUtils.signData(this.signedPreKeyPair.publicKey, this.signingKeyPair.privateKey);

    // Get a one-time pre-key
    const oneTimePreKeyId = Array.from(this.oneTimePreKeys.keys())[0];
    const oneTimePreKeyPair = this.oneTimePreKeys.get(oneTimePreKeyId);
    let oneTimePreKey;

    if (oneTimePreKeyPair) {
      oneTimePreKey = {
        id: oneTimePreKeyId,
        publicKey: await CryptoUtils.exportPublicKey(oneTimePreKeyPair.publicKey),
      };
    }

    return {
      identityKey: identityPublicKey,
      signedPreKey: {
        id: 1,
        publicKey: signedPreKeyPublic,
        signature: signature,
      },
      oneTimePreKey,
    };
  }

  

  /**
   * Initialize session with remote user's key bundle using libsodium crypto_kx
   */
  async initializeSession(conversationId: string, remoteKeyBundle: KeyBundle): Promise<SessionState> {
    if (!this.identityKeyPair) {
      throw new Error('Identity key not initialized');
    }

    try {
      console.log('🔐 Initializing session with libsodium crypto_kx...');

      // Import remote keys
      console.log('🔐 Importing remote identity key...');
      const remoteIdentityKey = await CryptoUtils.importPublicKey(remoteKeyBundle.identityKey);
      console.log('🔐 ✅ Remote identity key imported');

      // Derive session keys using libsodium crypto_kx (client side)
      console.log('🔐 Deriving session keys...');
      const sessionKeys = await CryptoUtils.deriveClientSessionKeys(
        this.identityKeyPair.publicKey,
        this.identityKeyPair.privateKey,
        remoteIdentityKey
      );
      console.log('🔐 ✅ Session keys derived');

      // Use the TX key as our encryption key for sending messages
      const rootKey = sessionKeys.sharedTx;

      console.log('🔐 Creating session state...');
      const sessionState: SessionState = {
        rootKey: await CryptoUtils.exportPrivateKey(rootKey),
        messageNumberSend: 0,
        messageNumberReceive: 0,
        previousChainLength: 0,
        ratchetKeyPair: {
          private: await CryptoUtils.exportPrivateKey(this.identityKeyPair.privateKey),
          public: await CryptoUtils.exportPublicKey(this.identityKeyPair.publicKey),
        },
      };

      // Store session
      this.sessions.set(conversationId, sessionState);
      console.log('🔐 ✅ Session state created and stored');

      return sessionState;
    } catch (error) {
      console.error('🔐 ❌ Session initialization failed:', error);
      console.error('🔐 Error details:', {
        conversationId,
        hasIdentityKeyPair: !!this.identityKeyPair,
        remoteKeyBundle: {
          hasIdentityKey: !!remoteKeyBundle.identityKey,
          hasSignedPreKey: !!remoteKeyBundle.signedPreKey,
          hasOneTimePreKey: !!remoteKeyBundle.oneTimePreKey,
        }
      });
      throw error;
    }
  }

  /**
   * Encrypt message using session state with libsodium
   */
  async encryptMessage(conversationId: string, content: string): Promise<EncryptedMessage> {
    const sessionState = this.sessions.get(conversationId);
    if (!sessionState) {
      throw new Error('Session not found for conversation');
    }

    // Use the root key directly as our encryption key
    const encryptionKey = await CryptoUtils.importPrivateKey(sessionState.rootKey);

    // Encrypt message using libsodium secretbox
    const { encryptedData, iv } = await CryptoUtils.encryptMessage(content, encryptionKey);

    // Update session state
    sessionState.messageNumberSend++;
    this.sessions.set(conversationId, sessionState);

    return {
      encryptedContent: encryptedData,
      iv,
      messageNumber: sessionState.messageNumberSend - 1,
      ratchetKey: sessionState.ratchetKeyPair?.public || '',
      previousChainLength: sessionState.previousChainLength,
    };
  }

  /**
   * Decrypt message using session state with libsodium
   */
  async decryptMessage(
    conversationId: string,
    encryptedMessage: EncryptedMessage
  ): Promise<string> {
    const sessionState = this.sessions.get(conversationId);
    if (!sessionState) {
      throw new Error('Session not found for conversation');
    }

    // Use the root key directly as our decryption key
    const decryptionKey = await CryptoUtils.importPrivateKey(sessionState.rootKey);

    // Decrypt message using libsodium secretbox
    const decryptedContent = await CryptoUtils.decryptMessage(
      encryptedMessage.encryptedContent,
      encryptedMessage.iv,
      decryptionKey
    );

    // Update session state
    if (encryptedMessage.messageNumber >= sessionState.messageNumberReceive) {
      sessionState.messageNumberReceive = encryptedMessage.messageNumber + 1;
      this.sessions.set(conversationId, sessionState);
    }

    return decryptedContent;
  }

  /**
   * Get session state for a conversation
   */
  getSessionState(conversationId: string): SessionState | undefined {
    return this.sessions.get(conversationId);
  }

  /**
   * Update session state for a conversation
   */
  updateSessionState(conversationId: string, sessionState: SessionState): void {
    this.sessions.set(conversationId, sessionState);
  }

  /**
   * Generate new one-time pre-keys
   */
  async generateOneTimePreKeys(count: number, startId: number = 0): Promise<Array<{key_id: number, public_key: string}>> {
    const preKeys = [];

    for (let i = 0; i < count; i++) {
      const keyId = startId + i;
      const keyPair = await CryptoUtils.generateKeyPair();
      this.oneTimePreKeys.set(keyId, keyPair);

      preKeys.push({
        key_id: keyId,
        public_key: await CryptoUtils.exportPublicKey(keyPair.publicKey),
      });
    }

    return preKeys;
  }

  /**
   * Rotate signed pre-key
   */
  async rotateSignedPreKey(): Promise<{id: number, publicKey: string, signature: string}> {
    if (!this.signingKeyPair) {
      throw new Error('Signing key not initialized');
    }

    // Generate new signed pre-key
    this.signedPreKeyPair = await CryptoUtils.generateKeyPair();
    const signedPreKeyPublic = await CryptoUtils.exportPublicKey(this.signedPreKeyPair.publicKey);

    // Sign with identity signing key (sign the raw bytes, not the base64 string)
    const signature = await CryptoUtils.signData(this.signedPreKeyPair.publicKey, this.signingKeyPair.privateKey);

    return {
      id: Date.now(), // Use timestamp as ID for simplicity
      publicKey: signedPreKeyPublic,
      signature: signature,
    };
  }

  /**
   * Clear all session data (for logout)
   */
  clearSessions(): void {
    this.sessions.clear();
  }

  /**
   * Export session state for storage
   */
  exportSessionState(conversationId: string): string | null {
    const sessionState = this.sessions.get(conversationId);
    return sessionState ? JSON.stringify(sessionState) : null;
  }

  /**
   * Import session state from storage
   */
  importSessionState(conversationId: string, sessionStateJson: string): void {
    try {
      const sessionState = JSON.parse(sessionStateJson) as SessionState;
      this.sessions.set(conversationId, sessionState);
    } catch (error) {
      console.error('Failed to import session state:', error);
    }
  }
}
