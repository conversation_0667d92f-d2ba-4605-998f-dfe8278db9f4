// frontend/src/utils/__tests__/libsodium-integration.test.ts
import { describe, it, expect, beforeEach } from 'vitest';
import { SignalProtocol } from '../signalProtocol';
import { CryptoUtils } from '../crypto';

describe('SignalProtocol with libsodium integration', () => {
  let alice: SignalProtocol;
  let bob: SignalProtocol;

  beforeEach(async () => {
    alice = new SignalProtocol();
    bob = new SignalProtocol();
    
    await alice.initializeKeys();
    await bob.initializeKeys();
  });

  it('should initialize keys successfully', async () => {
    const aliceKeyBundle = await alice.getKeyBundle();
    const bobKeyBundle = await bob.getKeyBundle();

    expect(aliceKeyBundle.identityKey).toBeDefined();
    expect(aliceKeyBundle.signedPreKey).toBeDefined();
    expect(bobKeyBundle.identityKey).toBeDefined();
    expect(bobKeyBundle.signedPreKey).toBeDefined();
  });

  it('should establish session and encrypt/decrypt messages', async () => {
    // Get key bundles
    const aliceKeyBundle = await alice.getKeyBundle();
    const bobKeyBundle = await bob.getKeyBundle();

    // Initialize sessions
    const aliceSession = await alice.initializeSession('test-conversation', bobKeyBundle);
    const bobSession = await bob.initializeSession('test-conversation', aliceKeyBundle);

    expect(aliceSession).toBeDefined();
    expect(bobSession).toBeDefined();

    // Alice sends a message to Bob
    const message = 'Hello Bob, this is Alice!';
    const encryptedMessage = await alice.encryptMessage('test-conversation', message);

    expect(encryptedMessage.encryptedContent).toBeDefined();
    expect(encryptedMessage.iv).toBeDefined();

    // Bob decrypts the message
    const decryptedMessage = await bob.decryptMessage('test-conversation', encryptedMessage);
    expect(decryptedMessage).toBe(message);
  });

  it('should handle multiple messages', async () => {
    // Get key bundles and initialize sessions
    const aliceKeyBundle = await alice.getKeyBundle();
    const bobKeyBundle = await bob.getKeyBundle();
    
    await alice.initializeSession('test-conversation', bobKeyBundle);
    await bob.initializeSession('test-conversation', aliceKeyBundle);

    // Send multiple messages
    const messages = ['Message 1', 'Message 2', 'Message 3'];
    
    for (const message of messages) {
      const encrypted = await alice.encryptMessage('test-conversation', message);
      const decrypted = await bob.decryptMessage('test-conversation', encrypted);
      expect(decrypted).toBe(message);
    }
  });

  it('should verify crypto support', async () => {
    const support = await CryptoUtils.checkCryptoSupport();
    
    expect(support.supportsX25519).toBe(true);
    expect(support.supportsEd25519).toBe(true);
  });

  it('should generate and verify signatures', async () => {
    const keyPair = await CryptoUtils.generateSigningKeyPair();
    const data = 'test data to sign';
    
    const signature = await CryptoUtils.signData(data, keyPair.privateKey);
    const isValid = await CryptoUtils.verifySignature(data, signature, keyPair.publicKey);
    
    expect(isValid).toBe(true);
    
    // Test with wrong data
    const isInvalid = await CryptoUtils.verifySignature('wrong data', signature, keyPair.publicKey);
    expect(isInvalid).toBe(false);
  });

  it('should export and import keys correctly', async () => {
    const keyPair = await CryptoUtils.generateKeyPair();
    
    const exportedPublic = await CryptoUtils.exportPublicKey(keyPair.publicKey);
    const exportedPrivate = await CryptoUtils.exportPrivateKey(keyPair.privateKey);
    
    expect(exportedPublic).toBeDefined();
    expect(exportedPrivate).toBeDefined();
    
    const importedPublic = await CryptoUtils.importPublicKey(exportedPublic);
    const importedPrivate = await CryptoUtils.importPrivateKey(exportedPrivate);
    
    expect(importedPublic).toBeDefined();
    expect(importedPrivate).toBeDefined();
  });

  it('should demonstrate the exact libsodium pattern from the working example', async () => {
    // This test follows the exact pattern from the working example
    
    // 1. Generate X25519 key pairs for Alice and Bob
    const aliceKeyPair = await CryptoUtils.generateKeyPair();
    const bobKeyPair = await CryptoUtils.generateKeyPair();

    console.log("Alice Public Key (base64):", await CryptoUtils.exportPublicKey(aliceKeyPair.publicKey));
    console.log("Bob Public Key (base64):", await CryptoUtils.exportPublicKey(bobKeyPair.publicKey));

    // 2. Derive session keys
    const aliceSessionKeys = await CryptoUtils.deriveClientSessionKeys(
      aliceKeyPair.publicKey,
      aliceKeyPair.privateKey,
      bobKeyPair.publicKey
    );
    const bobSessionKeys = await CryptoUtils.deriveServerSessionKeys(
      bobKeyPair.publicKey,
      bobKeyPair.privateKey,
      aliceKeyPair.publicKey
    );

    console.log(
      "Alice TX key == Bob RX key?",
      CryptoUtils.constantTimeEqual(aliceSessionKeys.sharedTx, bobSessionKeys.sharedRx)
    );
    console.log(
      "Bob TX key == Alice RX key?",
      CryptoUtils.constantTimeEqual(bobSessionKeys.sharedTx, aliceSessionKeys.sharedRx)
    );

    // Verify the session keys match correctly
    expect(CryptoUtils.constantTimeEqual(aliceSessionKeys.sharedTx, bobSessionKeys.sharedRx)).toBe(true);
    expect(CryptoUtils.constantTimeEqual(bobSessionKeys.sharedTx, aliceSessionKeys.sharedRx)).toBe(true);

    // 3. Alice encrypts a message for Bob
    const message = "Hello Bob, this is Alice!";
    const { encryptedData, iv } = await CryptoUtils.encryptMessage(message, aliceSessionKeys.sharedTx);

    console.log("Ciphertext (base64):", encryptedData);
    console.log("Nonce (base64):", iv);

    // 4. Bob decrypts the message
    const decrypted = await CryptoUtils.decryptMessage(encryptedData, iv, bobSessionKeys.sharedRx);

    console.log("Decrypted:", decrypted);
    expect(decrypted).toBe(message);
  });
});
