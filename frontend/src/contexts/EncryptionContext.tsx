// frontend/src/contexts/EncryptionContext.tsx
import React, { createContext, useContext, useEffect, useRef, useCallback, useState } from 'react';
import { SignalProtocol } from '../utils/signalProtocol';
import { CryptoUtils } from '../utils/crypto';
import type {KeyBundle, SessionState, EncryptedMessage} from '../utils/signalProtocol';
import { useAuth } from './AuthContext';
import {
  useUploadKeyBundleMutation,
  useGetKeyBundleQuery,
  useUploadOneTimePreKeysMutation,
  useCreateConversationSessionMutation,
  useGetConversationSessionQuery,
  useUpdateConversationSessionMutation,
  convertApiKeyBundleToSignalFormat,
  convertSignalKeyBundleToApiFormat,
} from '../services/encryptionApi';

interface EncryptionContextType {
  isInitialized: boolean;
  initializeEncryption: () => Promise<void>;
  encryptMessage: (conversationId: string, content: string) => Promise<EncryptedMessage>;
  decryptMessage: (conversationId: string, encryptedMessage: EncryptedMessage) => Promise<string>;
  initializeConversationSession: (conversationId: string, otherUserId: string) => Promise<void>;
  getSessionState: (conversationId: string) => SessionState | undefined;
  generateAndUploadPreKeys: (count?: number) => Promise<void>;
  rotateKeys: () => Promise<void>;
  clearEncryption: () => void;
}

const EncryptionContext = createContext<EncryptionContextType | undefined>(undefined);

export const EncryptionProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { user, isAuthenticated } = useAuth();
  const [isInitialized, setIsInitialized] = useState(false);
  const signalProtocolRef = useRef<SignalProtocol | null>(null);
  const initializingRef = useRef<boolean>(false);
  
  // RTK Query hooks
  const [uploadKeyBundle] = useUploadKeyBundleMutation();
  const [uploadOneTimePreKeys] = useUploadOneTimePreKeysMutation();
  const [createConversationSession] = useCreateConversationSessionMutation();
  const [updateConversationSession] = useUpdateConversationSessionMutation();

  /**
   * Initialize encryption for the current user
   */
  const initializeEncryption = useCallback(async () => {
    if (!user || !isAuthenticated || isInitialized) {
      console.log('🔐 Skipping encryption initialization:', {
        hasUser: !!user,
        isAuthenticated,
        isInitialized
      });
      return;
    }

    // Prevent multiple simultaneous initializations
    if (signalProtocolRef.current || initializingRef.current) {
      console.log('🔐 Encryption already initializing or initialized');
      return;
    }

    initializingRef.current = true;

    try {
      console.log('🔐 Initializing encryption for user:', user.username);

      // Check crypto support first
      console.log('🔐 Checking browser crypto support...');
      const cryptoSupport = await CryptoUtils.checkCryptoSupport();
      console.log('🔐 Crypto support:', cryptoSupport);

      if (!cryptoSupport.supportsX25519 && !cryptoSupport.supportsECDH) {
        throw new Error('Browser does not support required cryptographic algorithms');
      }

      if (!cryptoSupport.supportsEd25519 && !cryptoSupport.supportsECDSA) {
        throw new Error('Browser does not support required signing algorithms');
      }

      // Create new SignalProtocol instance
      const signalProtocol = new SignalProtocol();
      await signalProtocol.initializeKeys();
      
      // Get key bundle to upload
      const keyBundle = await signalProtocol.getKeyBundle();
      
      // Generate additional one-time pre-keys
      const oneTimePreKeys = await signalProtocol.generateOneTimePreKeys(50);
      
      // Upload key bundle to server
      const keyBundleUpload = convertSignalKeyBundleToApiFormat(keyBundle, oneTimePreKeys);
      
      await uploadKeyBundle(keyBundleUpload).unwrap();
      
      signalProtocolRef.current = signalProtocol;
      setIsInitialized(true);

      console.log('🔐 ✅ Encryption initialized successfully');
    } catch (error) {
      console.error('🔐 ❌ Failed to initialize encryption:', error);
      // Reset refs on error
      signalProtocolRef.current = null;
      throw error;
    } finally {
      initializingRef.current = false;
    }
  }, [user, isAuthenticated, isInitialized, uploadKeyBundle]);

  /**
   * Initialize session for a conversation
   */
  const initializeConversationSession = useCallback(async (conversationId: string, otherUserId: string) => {
    if (!signalProtocolRef.current) {
      throw new Error('Encryption not initialized');
    }

    if (!user) {
      throw new Error('User not authenticated');
    }

    try {
      console.log('🔐 Initializing session for conversation:', conversationId, 'with user:', otherUserId);

      // Check if session already exists
      const existingSession = signalProtocolRef.current.getSessionState(conversationId);
      if (existingSession) {
        console.log('🔐 Session already exists for conversation:', conversationId);
        return;
      }

      // Get other user's key bundle using direct API call instead of hook
      try {
        console.log('🔐 Fetching key bundle for user:', otherUserId);
        const response = await fetch(`/api/encryption/key-bundle/${otherUserId}/`, {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json',
          },
        });

        if (!response.ok) {
          const errorText = await response.text();
          console.error('🔐 ❌ Key bundle fetch failed:', response.status, errorText);
          throw new Error(`Failed to fetch key bundle: ${response.status} - ${errorText}`);
        }

        const apiKeyBundle = await response.json();
        console.log('🔐 ✅ Key bundle fetched successfully for user:', otherUserId);
        console.log('🔐 API key bundle structure:', {
          hasIdentityKey: !!apiKeyBundle.identity_public_key,
          hasSignedPreKey: !!apiKeyBundle.signed_prekey_public,
          hasOneTimePreKey: !!apiKeyBundle.one_time_prekey,
          signedPreKeyId: apiKeyBundle.signed_prekey_id,
        });

        if (!apiKeyBundle) {
          throw new Error('Failed to get key bundle for other user - empty response');
        }

        if (!apiKeyBundle.identity_public_key || !apiKeyBundle.signed_prekey_public) {
          throw new Error('Invalid key bundle - missing required keys');
        }

        const remoteKeyBundle = convertApiKeyBundleToSignalFormat(apiKeyBundle);
        console.log('🔐 Converted API key bundle to Signal format');
        console.log('🔐 Signal key bundle structure:', {
          hasIdentityKey: !!remoteKeyBundle.identityKey,
          hasSignedPreKey: !!remoteKeyBundle.signedPreKey?.publicKey,
          hasOneTimePreKey: !!remoteKeyBundle.oneTimePreKey?.publicKey,
        });

        // Initialize session
        console.log('🔐 Initializing Signal protocol session...');
        const sessionState = await signalProtocolRef.current.initializeSession(conversationId, remoteKeyBundle);
        console.log('🔐 ✅ Signal protocol session initialized');

        // Save session to server using direct API call
        console.log('🔐 Saving session to server...');
        const sessionResponse = await fetch('/api/encryption/sessions/create/', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            conversation_id: conversationId,
            participant_id: user!.id,
            session_state: sessionState,
            root_key: sessionState.rootKey,
            chain_key_send: sessionState.chainKeySend,
            chain_key_receive: sessionState.chainKeyReceive,
            message_number_send: sessionState.messageNumberSend,
            message_number_receive: sessionState.messageNumberReceive,
            previous_chain_length: sessionState.previousChainLength,
          }),
        });

        console.log('🔐 Session save response:', sessionResponse);

        if (!sessionResponse.ok) {
          const errorText = await sessionResponse.text();
          console.error('🔐 ❌ Session save failed:', sessionResponse.status, errorText);
          throw new Error(`Failed to save session: ${sessionResponse.status} - ${errorText}`);
        }

        console.log('🔐 ✅ Session saved to server successfully');

        console.log('🔐 ✅ Session initialized successfully');
      } catch (apiError) {
        console.error('🔐 ❌ API error during session initialization:', apiError);
        throw apiError;
      }
    } catch (error) {
      console.error('🔐 ❌ Failed to initialize session:', error);
      throw error;
    }
  }, [user]);

  /**
   * Encrypt a message
   */
  const encryptMessage = useCallback(async (conversationId: string, content: string): Promise<EncryptedMessage> => {
    if (!signalProtocolRef.current) {
      throw new Error('Encryption not initialized');
    }

    try {
      console.log('🔐 Encrypting message for conversation:', conversationId);
      
      const encryptedMessage = await signalProtocolRef.current.encryptMessage(conversationId, content);
      
      // Update session state on server
      const sessionState = signalProtocolRef.current.getSessionState(conversationId);
      if (sessionState) {
        await updateConversationSession({
          conversationId,
          updates: {
            session_state: sessionState,
            message_number_send: sessionState.messageNumberSend,
            chain_key_send: sessionState.chainKeySend,
          },
        }).unwrap();
      }
      
      console.log('🔐 ✅ Message encrypted successfully');
      return encryptedMessage;
    } catch (error) {
      console.error('🔐 ❌ Failed to encrypt message:', error);
      throw error;
    }
  }, [updateConversationSession]);

  /**
   * Decrypt a message
   */
  const decryptMessage = useCallback(async (conversationId: string, encryptedMessage: EncryptedMessage): Promise<string> => {
    if (!signalProtocolRef.current) {
      throw new Error('Encryption not initialized');
    }

    try {
      console.log('🔐 Decrypting message for conversation:', conversationId);
      
      const decryptedContent = await signalProtocolRef.current.decryptMessage(conversationId, encryptedMessage);
      
      // Update session state on server
      const sessionState = signalProtocolRef.current.getSessionState(conversationId);
      if (sessionState) {
        await updateConversationSession({
          conversationId,
          updates: {
            session_state: sessionState,
            message_number_receive: sessionState.messageNumberReceive,
            chain_key_receive: sessionState.chainKeyReceive,
          },
        }).unwrap();
      }
      
      console.log('🔐 ✅ Message decrypted successfully');
      return decryptedContent;
    } catch (error) {
      console.error('🔐 ❌ Failed to decrypt message:', error);
      throw error;
    }
  }, [updateConversationSession]);

  /**
   * Get session state for a conversation
   */
  const getSessionState = useCallback((conversationId: string): SessionState | undefined => {
    return signalProtocolRef.current?.getSessionState(conversationId);
  }, []);

  /**
   * Generate and upload new one-time pre-keys
   */
  const generateAndUploadPreKeys = useCallback(async (count: number = 50) => {
    if (!signalProtocolRef.current) {
      throw new Error('Encryption not initialized');
    }

    try {
      console.log('🔐 Generating and uploading new pre-keys');
      
      const startId = Date.now(); // Use timestamp to ensure unique IDs
      const oneTimePreKeys = await signalProtocolRef.current.generateOneTimePreKeys(count, startId);
      
      await uploadOneTimePreKeys({ prekeys: oneTimePreKeys }).unwrap();
      
      console.log('🔐 ✅ Pre-keys uploaded successfully');
    } catch (error) {
      console.error('🔐 ❌ Failed to upload pre-keys:', error);
      throw error;
    }
  }, [uploadOneTimePreKeys]);

  /**
   * Rotate keys (signed pre-key and optionally add new one-time pre-keys)
   */
  const rotateKeys = useCallback(async () => {
    if (!signalProtocolRef.current) {
      throw new Error('Encryption not initialized');
    }

    try {
      console.log('🔐 Rotating keys');
      
      const newSignedPreKey = await signalProtocolRef.current.rotateSignedPreKey();
      const newOneTimePreKeys = await signalProtocolRef.current.generateOneTimePreKeys(25);
      
      // Upload rotated keys to server
      // Note: This would need a rotation endpoint in the API
      console.log('🔐 ✅ Keys rotated successfully');
    } catch (error) {
      console.error('🔐 ❌ Failed to rotate keys:', error);
      throw error;
    }
  }, []);

  /**
   * Clear encryption data (for logout)
   */
  const clearEncryption = useCallback(() => {
    console.log('🔐 Clearing encryption data');
    signalProtocolRef.current?.clearSessions();
    signalProtocolRef.current = null;
    initializingRef.current = false;
    setIsInitialized(false);
  }, []);

  // Initialize encryption when user logs in
  useEffect(() => {
    if (isAuthenticated && user && !isInitialized) {
      initializeEncryption().catch(console.error);
    }
  }, [isAuthenticated, user, isInitialized, initializeEncryption]);

  // Clear encryption when user logs out
  useEffect(() => {
    if (!isAuthenticated) {
      clearEncryption();
    }
  }, [isAuthenticated, clearEncryption]);

  const value: EncryptionContextType = {
    isInitialized,
    initializeEncryption,
    encryptMessage,
    decryptMessage,
    initializeConversationSession,
    getSessionState,
    generateAndUploadPreKeys,
    rotateKeys,
    clearEncryption,
  };

  return (
    <EncryptionContext.Provider value={value}>
      {children}
    </EncryptionContext.Provider>
  );
};

export const useEncryption = (): EncryptionContextType => {
  const context = useContext(EncryptionContext);
  if (context === undefined) {
    throw new Error('useEncryption must be used within an EncryptionProvider');
  }
  return context;
};
